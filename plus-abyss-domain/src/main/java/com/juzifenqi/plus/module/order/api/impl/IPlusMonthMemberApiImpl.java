package com.juzifenqi.plus.module.order.api.impl;

import com.juzifenqi.plus.api.IPlusMonthMemberApi;
import com.juzifenqi.plus.dubbo.DubboService;
import com.juzifenqi.plus.module.order.application.IPlusMonthMemberRenewalApplication;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@DubboService
public class IPlusMonthMemberApiImpl implements IPlusMonthMemberApi {

    @Autowired
    private IPlusMonthMemberRenewalApplication plusMonthMemberRenewalApplication;


    @Override
    public void createRenewalOrdersBySchedule() {
        
    }
}
